﻿import { Layout } from "@/components/Layout";
import { useNavigate } from "react-router-dom";
import { useLanguage } from "@/components/LanguageContext";
import { Button } from "@/components/ui/button";

const CropAllocation = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();

  return (
    <Layout>
      <main className="container mx-auto px-4 pb-10">
        <div className="max-w-7xl mx-auto pt-6">
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
              {t("crop-allocation")}
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              AI-powered crop planning with real-time insights, market analysis, and predictive recommendations
            </p>
          </div>
          
          <div className="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
            <div className="p-8">
              <div className="text-center">
                <h2 className="text-2xl font-semibold mb-4">Crop Allocation Tool</h2>
                <p className="text-gray-600 mb-6">
                  Welcome to the Crop Allocation page! This tool helps you plan your crop allocation with AI-powered recommendations.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                  <div className="bg-green-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-green-800 mb-2">Land Analysis</h3>
                    <p className="text-green-600 text-sm">Analyze your soil and land conditions</p>
                  </div>
                  <div className="bg-blue-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-blue-800 mb-2">AI Recommendations</h3>
                    <p className="text-blue-600 text-sm">Get smart crop suggestions</p>
                  </div>
                  <div className="bg-purple-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-purple-800 mb-2">Visualization</h3>
                    <p className="text-purple-600 text-sm">See your crop allocation plan</p>
                  </div>
                </div>
                <Button 
                  onClick={() => navigate("/market")}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Continue to Market
                </Button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </Layout>
  );
};

export default CropAllocation;
