import * as T from '../type/index';
type TDereference<Context extends T.TProperties, Key extends string> = (Key extends keyof Context ? Context[Key] : T.TRef<Key>);
type TDelimitedDecode<Input extends ([unknown, unknown] | unknown)[], Result extends unknown[] = []> = (Input extends [infer Left, ...infer Right] ? Left extends [infer Item, infer _] ? TDelimitedDecode<Right, [...Result, Item]> : TDelimitedDecode<Right, [...Result, Left]> : Result);
type TDelimited<Input extends [unknown, unknown]> = Input extends [infer Left extends unknown[], infer Right extends unknown[]] ? TDelimitedDecode<[...Left, ...Right]> : [];
export type TGenericReferenceParameterListMapping<Input extends [unknown, unknown], Context extends T.TProperties> = TDelimited<Input>;
export declare function GenericReferenceParameterListMapping(input: [unknown, unknown], context: unknown): unknown[];
export type TGenericReferenceMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties, Result = Context extends T.TProperties ? Input extends [infer Reference extends string, '<', infer Args extends T.TSchema[], '>'] ? T.TInstantiate<TDereference<Context, Reference>, Args> : never : never> = Result;
export declare function GenericReferenceMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TSchema;
export type TGenericArgumentsListMapping<Input extends [unknown, unknown], Context extends T.TProperties> = TDelimited<Input>;
export declare function GenericArgumentsListMapping(input: [unknown, unknown], context: unknown): unknown[];
type GenericArgumentsContext<Arguments extends string[], Context extends T.TProperties, Result extends T.TProperties = {}> = (Arguments extends [...infer Left extends string[], infer Right extends string] ? GenericArgumentsContext<Left, Context, Result & {
    [_ in Right]: T.TArgument<Left['length']>;
}> : T.Evaluate<Result & Context>);
export type TGenericArgumentsMapping<Input extends [unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['<', infer Arguments extends string[], '>'] ? Context extends infer Context extends T.TProperties ? GenericArgumentsContext<Arguments, Context> : never : never;
declare const GenericArgumentsContext: (_arguments: string[], context: T.TProperties) => T.TProperties;
export declare function GenericArgumentsMapping(input: [unknown, unknown, unknown], context: unknown): T.TProperties;
export type TKeywordStringMapping<Input extends 'string', Context extends T.TProperties> = T.TString;
export declare function KeywordStringMapping(input: 'string', context: unknown): T.TString;
export type TKeywordNumberMapping<Input extends 'number', Context extends T.TProperties> = T.TNumber;
export declare function KeywordNumberMapping(input: 'number', context: unknown): T.TNumber;
export type TKeywordBooleanMapping<Input extends 'boolean', Context extends T.TProperties> = T.TBoolean;
export declare function KeywordBooleanMapping(input: 'boolean', context: unknown): T.TBoolean;
export type TKeywordUndefinedMapping<Input extends 'undefined', Context extends T.TProperties> = T.TUndefined;
export declare function KeywordUndefinedMapping(input: 'undefined', context: unknown): T.TUndefined;
export type TKeywordNullMapping<Input extends 'null', Context extends T.TProperties> = T.TNull;
export declare function KeywordNullMapping(input: 'null', context: unknown): T.TNull;
export type TKeywordIntegerMapping<Input extends 'integer', Context extends T.TProperties> = T.TInteger;
export declare function KeywordIntegerMapping(input: 'integer', context: unknown): T.TInteger;
export type TKeywordBigIntMapping<Input extends 'bigint', Context extends T.TProperties> = T.TBigInt;
export declare function KeywordBigIntMapping(input: 'bigint', context: unknown): T.TBigInt;
export type TKeywordUnknownMapping<Input extends 'unknown', Context extends T.TProperties> = T.TUnknown;
export declare function KeywordUnknownMapping(input: 'unknown', context: unknown): T.TUnknown;
export type TKeywordAnyMapping<Input extends 'any', Context extends T.TProperties> = T.TAny;
export declare function KeywordAnyMapping(input: 'any', context: unknown): T.TAny;
export type TKeywordNeverMapping<Input extends 'never', Context extends T.TProperties> = T.TNever;
export declare function KeywordNeverMapping(input: 'never', context: unknown): T.TNever;
export type TKeywordSymbolMapping<Input extends 'symbol', Context extends T.TProperties> = T.TSymbol;
export declare function KeywordSymbolMapping(input: 'symbol', context: unknown): T.TSymbol;
export type TKeywordVoidMapping<Input extends 'void', Context extends T.TProperties> = T.TVoid;
export declare function KeywordVoidMapping(input: 'void', context: unknown): T.TVoid;
export type TKeywordMapping<Input extends unknown, Context extends T.TProperties> = Input;
export declare function KeywordMapping(input: unknown, context: unknown): unknown;
export type TLiteralStringMapping<Input extends string, Context extends T.TProperties> = Input extends T.TLiteralValue ? T.TLiteral<Input> : never;
export declare function LiteralStringMapping(input: string, context: unknown): T.TLiteral<string>;
export type TLiteralNumberMapping<Input extends string, Context extends T.TProperties> = Input extends `${infer Value extends number}` ? T.TLiteral<Value> : never;
export declare function LiteralNumberMapping(input: string, context: unknown): T.TLiteral<number>;
export type TLiteralBooleanMapping<Input extends 'true' | 'false', Context extends T.TProperties> = Input extends 'true' ? T.TLiteral<true> : T.TLiteral<false>;
export declare function LiteralBooleanMapping(input: 'true' | 'false', context: unknown): T.TLiteral<boolean>;
export type TLiteralMapping<Input extends unknown, Context extends T.TProperties> = Input;
export declare function LiteralMapping(input: unknown, context: unknown): unknown;
export type TKeyOfMapping<Input extends [unknown] | [], Context extends T.TProperties> = Input extends [unknown] ? true : false;
export declare function KeyOfMapping(input: [unknown] | [], context: unknown): boolean;
type TIndexArrayMappingReduce<Input extends unknown[], Result extends unknown[] = []> = (Input extends [infer Left extends unknown, ...infer Right extends unknown[]] ? Left extends ['[', infer Type extends T.TSchema, ']'] ? TIndexArrayMappingReduce<Right, [...Result, [Type]]> : TIndexArrayMappingReduce<Right, [...Result, []]> : Result);
export type TIndexArrayMapping<Input extends ([unknown, unknown, unknown] | [unknown, unknown])[], Context extends T.TProperties> = Input extends unknown[] ? TIndexArrayMappingReduce<Input> : [];
export declare function IndexArrayMapping(input: ([unknown, unknown, unknown] | [unknown, unknown])[], context: unknown): unknown[];
export type TExtendsMapping<Input extends [unknown, unknown, unknown, unknown, unknown, unknown] | [], Context extends T.TProperties> = Input extends ['extends', infer Type extends T.TSchema, '?', infer True extends T.TSchema, ':', infer False extends T.TSchema] ? [Type, True, False] : [];
export declare function ExtendsMapping(input: [unknown, unknown, unknown, unknown, unknown, unknown] | [], context: unknown): unknown[];
export type TBaseMapping<Input extends [unknown, unknown, unknown] | unknown, Context extends T.TProperties> = (Input extends ['(', infer Type extends T.TSchema, ')'] ? Type : Input extends infer Type extends T.TSchema ? Type : never);
export declare function BaseMapping(input: [unknown, unknown, unknown] | unknown, context: unknown): unknown;
type TFactorIndexArray<Type extends T.TSchema, IndexArray extends unknown[]> = (IndexArray extends [...infer Left extends unknown[], infer Right extends T.TSchema[]] ? (Right extends [infer Indexer extends T.TSchema] ? T.TIndex<TFactorIndexArray<Type, Left>, T.TIndexPropertyKeys<Indexer>> : Right extends [] ? T.TArray<TFactorIndexArray<Type, Left>> : T.TNever) : Type);
type TFactorExtends<Type extends T.TSchema, Extends extends unknown[]> = (Extends extends [infer Right extends T.TSchema, infer True extends T.TSchema, infer False extends T.TSchema] ? T.TExtends<Type, Right, True, False> : Type);
export type TFactorMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends [infer KeyOf extends boolean, infer Type extends T.TSchema, infer IndexArray extends unknown[], infer Extends extends unknown[]] ? KeyOf extends true ? TFactorExtends<T.TKeyOf<TFactorIndexArray<Type, IndexArray>>, Extends> : TFactorExtends<TFactorIndexArray<Type, IndexArray>, Extends> : never;
export declare function FactorMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TSchema;
type TExprBinaryMapping<Left extends T.TSchema, Rest extends unknown[]> = (Rest extends [infer Operator extends unknown, infer Right extends T.TSchema, infer Next extends unknown[]] ? (TExprBinaryMapping<Right, Next> extends infer Schema extends T.TSchema ? (Operator extends '&' ? (Schema extends T.TIntersect<infer Types extends T.TSchema[]> ? T.TIntersect<[Left, ...Types]> : T.TIntersect<[Left, Schema]>) : Operator extends '|' ? (Schema extends T.TUnion<infer Types extends T.TSchema[]> ? T.TUnion<[Left, ...Types]> : T.TUnion<[Left, Schema]>) : never) : never) : Left);
export type TExprTermTailMapping<Input extends [unknown, unknown, unknown] | [], Context extends T.TProperties> = Input;
export declare function ExprTermTailMapping(input: [unknown, unknown, unknown] | [], context: unknown): [] | [unknown, unknown, unknown];
export type TExprTermMapping<Input extends [unknown, unknown], Context extends T.TProperties> = (Input extends [infer Left extends T.TSchema, infer Rest extends unknown[]] ? TExprBinaryMapping<Left, Rest> : []);
export declare function ExprTermMapping(input: [unknown, unknown], context: unknown): T.TSchema;
export type TExprTailMapping<Input extends [unknown, unknown, unknown] | [], Context extends T.TProperties> = Input;
export declare function ExprTailMapping(input: [unknown, unknown, unknown] | [], context: unknown): [] | [unknown, unknown, unknown];
export type TExprMapping<Input extends [unknown, unknown], Context extends T.TProperties> = Input extends [infer Left extends T.TSchema, infer Rest extends unknown[]] ? TExprBinaryMapping<Left, Rest> : [];
export declare function ExprMapping(input: [unknown, unknown], context: unknown): T.TSchema;
export type TTypeMapping<Input extends unknown, Context extends T.TProperties> = Input;
export declare function TypeMapping(input: unknown, context: unknown): unknown;
export type TPropertyKeyMapping<Input extends string, Context extends T.TProperties> = Input;
export declare function PropertyKeyMapping(input: string, context: unknown): string;
export type TReadonlyMapping<Input extends [unknown] | [], Context extends T.TProperties> = Input extends [unknown] ? true : false;
export declare function ReadonlyMapping(input: [unknown] | [], context: unknown): boolean;
export type TOptionalMapping<Input extends [unknown] | [], Context extends T.TProperties> = Input extends [unknown] ? true : false;
export declare function OptionalMapping(input: [unknown] | [], context: unknown): boolean;
export type TPropertyMapping<Input extends [unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends [infer IsReadonly extends boolean, infer Key extends string, infer IsOptional extends boolean, string, infer Type extends T.TSchema] ? {
    [_ in Key]: ([
        IsReadonly,
        IsOptional
    ] extends [true, true] ? T.TReadonlyOptional<Type> : [
        IsReadonly,
        IsOptional
    ] extends [true, false] ? T.TReadonly<Type> : [
        IsReadonly,
        IsOptional
    ] extends [false, true] ? T.TOptional<Type> : Type);
} : never;
export declare function PropertyMapping(input: [unknown, unknown, unknown, unknown, unknown], context: unknown): {
    [x: string]: T.TSchema;
};
export type TPropertyDelimiterMapping<Input extends [unknown, unknown] | [unknown], Context extends T.TProperties> = Input;
export declare function PropertyDelimiterMapping(input: [unknown, unknown] | [unknown], context: unknown): [unknown] | [unknown, unknown];
export type TPropertyListMapping<Input extends [unknown, unknown], Context extends T.TProperties> = TDelimited<Input>;
export declare function PropertyListMapping(input: [unknown, unknown], context: unknown): unknown[];
type TObjectMappingReduce<PropertiesList extends T.TProperties[], Result extends T.TProperties = {}> = (PropertiesList extends [infer Left extends T.TProperties, ...infer Right extends T.TProperties[]] ? TObjectMappingReduce<Right, Result & Left> : {
    [Key in keyof Result]: Result[Key];
});
export type TObjectMapping<Input extends [unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['{', infer PropertyList extends T.TProperties[], '}'] ? T.TObject<TObjectMappingReduce<PropertyList>> : never;
export declare function ObjectMapping(input: [unknown, unknown, unknown], context: unknown): T.TObject<T.TProperties>;
export type TElementListMapping<Input extends [unknown, unknown], Context extends T.TProperties> = TDelimited<Input>;
export declare function ElementListMapping(input: [unknown, unknown], context: unknown): unknown[];
export type TTupleMapping<Input extends [unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['[', infer Types extends T.TSchema[], ']'] ? T.TTuple<Types> : never;
export declare function TupleMapping(input: [unknown, unknown, unknown], context: unknown): T.TTuple<T.TSchema[]>;
export type TParameterMapping<Input extends [unknown, unknown, unknown], Context extends T.TProperties> = Input extends [string, ':', infer Type extends T.TSchema] ? Type : never;
export declare function ParameterMapping(input: [unknown, unknown, unknown], context: unknown): T.TSchema;
export type TParameterListMapping<Input extends [unknown, unknown], Context extends T.TProperties> = TDelimited<Input>;
export declare function ParameterListMapping(input: [unknown, unknown], context: unknown): unknown[];
export type TFunctionMapping<Input extends [unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['(', infer ParameterList extends T.TSchema[], ')', '=>', infer ReturnType extends T.TSchema] ? T.TFunction<ParameterList, ReturnType> : never;
export declare function FunctionMapping(input: [unknown, unknown, unknown, unknown, unknown], context: unknown): T.TFunction<T.TSchema[], T.TSchema>;
export type TConstructorMapping<Input extends [unknown, unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['new', '(', infer ParameterList extends T.TSchema[], ')', '=>', infer InstanceType extends T.TSchema] ? T.TConstructor<ParameterList, InstanceType> : never;
export declare function ConstructorMapping(input: [unknown, unknown, unknown, unknown, unknown, unknown], context: unknown): T.TConstructor<T.TSchema[], T.TSchema>;
export type TMappedMapping<Input extends [unknown, unknown, unknown, unknown, unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['{', '[', infer _Key extends string, 'in', infer _Right extends T.TSchema, ']', ':', infer _Type extends T.TSchema, '}'] ? T.TLiteral<'Mapped types not supported'> : never;
export declare function MappedMapping(input: [unknown, unknown, unknown, unknown, unknown, unknown, unknown, unknown, unknown], context: unknown): T.TLiteral<"Mapped types not supported">;
export type TAsyncIteratorMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['AsyncIterator', '<', infer Type extends T.TSchema, '>'] ? T.TAsyncIterator<Type> : never;
export declare function AsyncIteratorMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TAsyncIterator<T.TSchema>;
export type TIteratorMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Iterator', '<', infer Type extends T.TSchema, '>'] ? T.TIterator<Type> : never;
export declare function IteratorMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TIterator<T.TSchema>;
export type TArgumentMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Argument', '<', infer Type extends T.TSchema, '>'] ? Type extends T.TLiteral<infer Index extends number> ? T.TArgument<Index> : T.TNever : never;
export declare function ArgumentMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TNever | T.TArgument<number>;
export type TAwaitedMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Awaited', '<', infer Type extends T.TSchema, '>'] ? T.TAwaited<Type> : never;
export declare function AwaitedMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TSchema;
export type TArrayMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Array', '<', infer Type extends T.TSchema, '>'] ? T.TArray<Type> : never;
export declare function ArrayMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TArray<T.TSchema>;
export type TRecordMapping<Input extends [unknown, unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Record', '<', infer Key extends T.TSchema, ',', infer Type extends T.TSchema, '>'] ? T.TRecordOrObject<Key, Type> : never;
export declare function RecordMapping(input: [unknown, unknown, unknown, unknown, unknown, unknown], context: unknown): T.TNever;
export type TPromiseMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Promise', '<', infer Type extends T.TSchema, '>'] ? T.TPromise<Type> : never;
export declare function PromiseMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TPromise<T.TSchema>;
export type TConstructorParametersMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['ConstructorParameters', '<', infer Type extends T.TSchema, '>'] ? T.TConstructorParameters<Type> : never;
export declare function ConstructorParametersMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TNever;
export type TFunctionParametersMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Parameters', '<', infer Type extends T.TSchema, '>'] ? T.TParameters<Type> : never;
export declare function FunctionParametersMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TNever;
export type TInstanceTypeMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['InstanceType', '<', infer Type extends T.TSchema, '>'] ? T.TInstanceType<Type> : never;
export declare function InstanceTypeMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TNever;
export type TReturnTypeMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['ReturnType', '<', infer Type extends T.TSchema, '>'] ? T.TReturnType<Type> : never;
export declare function ReturnTypeMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TNever;
export type TPartialMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Partial', '<', infer Type extends T.TSchema, '>'] ? T.TPartial<Type> : never;
export declare function PartialMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TObject<{}>;
export type TRequiredMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Required', '<', infer Type extends T.TSchema, '>'] ? T.TRequired<Type> : never;
export declare function RequiredMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TObject<{}>;
export type TPickMapping<Input extends [unknown, unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Pick', '<', infer Type extends T.TSchema, ',', infer Key extends T.TSchema, '>'] ? T.TPick<Type, Key> : never;
export declare function PickMapping(input: [unknown, unknown, unknown, unknown, unknown, unknown], context: unknown): T.TObject<{}>;
export type TOmitMapping<Input extends [unknown, unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Omit', '<', infer Type extends T.TSchema, ',', infer Key extends T.TSchema, '>'] ? T.TOmit<Type, Key> : never;
export declare function OmitMapping(input: [unknown, unknown, unknown, unknown, unknown, unknown], context: unknown): T.TObject<{}>;
export type TExcludeMapping<Input extends [unknown, unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Exclude', '<', infer Type extends T.TSchema, ',', infer Key extends T.TSchema, '>'] ? T.TExclude<Type, Key> : never;
export declare function ExcludeMapping(input: [unknown, unknown, unknown, unknown, unknown, unknown], context: unknown): T.TNever;
export type TExtractMapping<Input extends [unknown, unknown, unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Extract', '<', infer Type extends T.TSchema, ',', infer Key extends T.TSchema, '>'] ? T.TExtract<Type, Key> : never;
export declare function ExtractMapping(input: [unknown, unknown, unknown, unknown, unknown, unknown], context: unknown): T.TSchema;
export type TUppercaseMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Uppercase', '<', infer Type extends T.TSchema, '>'] ? T.TUppercase<Type> : never;
export declare function UppercaseMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TSchema;
export type TLowercaseMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Lowercase', '<', infer Type extends T.TSchema, '>'] ? T.TLowercase<Type> : never;
export declare function LowercaseMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TSchema;
export type TCapitalizeMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Capitalize', '<', infer Type extends T.TSchema, '>'] ? T.TCapitalize<Type> : never;
export declare function CapitalizeMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TSchema;
export type TUncapitalizeMapping<Input extends [unknown, unknown, unknown, unknown], Context extends T.TProperties> = Input extends ['Uncapitalize', '<', infer Type extends T.TSchema, '>'] ? T.TUncapitalize<Type> : never;
export declare function UncapitalizeMapping(input: [unknown, unknown, unknown, unknown], context: unknown): T.TSchema;
export type TDateMapping<Input extends 'Date', Context extends T.TProperties> = T.TDate;
export declare function DateMapping(input: 'Date', context: unknown): T.TDate;
export type TUint8ArrayMapping<Input extends 'Uint8Array', Context extends T.TProperties> = T.TUint8Array;
export declare function Uint8ArrayMapping(input: 'Uint8Array', context: unknown): T.TUint8Array;
export type TReferenceMapping<Input extends string, Context extends T.TProperties> = Context extends T.TProperties ? Input extends string ? TDereference<Context, Input> : never : never;
export declare function ReferenceMapping(input: string, context: unknown): T.TSchema;
export {};
