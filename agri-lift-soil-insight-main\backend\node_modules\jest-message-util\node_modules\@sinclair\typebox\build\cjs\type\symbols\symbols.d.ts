/** Symbol key applied to transform types */
export declare const TransformKind: unique symbol;
/** Symbol key applied to readonly types */
export declare const ReadonlyKind: unique symbol;
/** Symbol key applied to optional types */
export declare const OptionalKind: unique symbol;
/** Symbol key applied to types */
export declare const Hint: unique symbol;
/** Symbol key applied to types */
export declare const Kind: unique symbol;
