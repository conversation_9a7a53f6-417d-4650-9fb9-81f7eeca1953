{"version": 3, "sources": ["../../@radix-ui/react-tabs/src/Tabs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value?: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ElementRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AA4Ff,yBAAA;AA3ER,IAAM,YAAY;AAGlB,IAAM,CAAC,mBAAmB,eAAe,IAAI,mBAAmB,WAAW;EACzE;AACF,CAAC;AACD,IAAM,2BAA2B,4BAA4B;AAW7D,IAAM,CAAC,cAAc,cAAc,IAAI,kBAAoC,SAAS;AA6BpF,IAAM,OAAa;EACjB,CAAC,OAA+B,iBAAiB;AAC/C,UAAM;MACJ;MACA,OAAO;MACP;MACA;MACA,cAAc;MACd;MACA,iBAAiB;MACjB,GAAG;IACL,IAAI;AACJ,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;MAC7C,MAAM;MACN,UAAU;MACV,aAAa;IACf,CAAC;AAED,eACE;MAAC;MAAA;QACC,OAAO;QACP,QAAQ,MAAM;QACd;QACA,eAAe;QACf;QACA,KAAK;QACL;QAEA,cAAA;UAAC,UAAU;UAAV;YACC,KAAK;YACL,oBAAkB;YACjB,GAAG;YACJ,KAAK;UAAA;QACP;MAAA;IACF;EAEJ;AACF;AAEA,KAAK,cAAc;AAMnB,IAAM,gBAAgB;AAOtB,IAAM,WAAiB;EACrB,CAAC,OAAmC,iBAAiB;AACnD,UAAM,EAAE,aAAa,OAAO,MAAM,GAAG,UAAU,IAAI;AACnD,UAAM,UAAU,eAAe,eAAe,WAAW;AACzD,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,eACE;MAAkB;MAAjB;QACC,SAAO;QACN,GAAG;QACJ,aAAa,QAAQ;QACrB,KAAK,QAAQ;QACb;QAEA,cAAA;UAAC,UAAU;UAAV;YACC,MAAK;YACL,oBAAkB,QAAQ;YACzB,GAAG;YACJ,KAAK;UAAA;QACP;MAAA;IACF;EAEJ;AACF;AAEA,SAAS,cAAc;AAMvB,IAAM,eAAe;AAQrB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,aAAa,OAAO,WAAW,OAAO,GAAG,aAAa,IAAI;AAClE,UAAM,UAAU,eAAe,cAAc,WAAW;AACxD,UAAM,wBAAwB,yBAAyB,WAAW;AAClE,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,aAAa,UAAU,QAAQ;AACrC,eACE;MAAkB;MAAjB;QACC,SAAO;QACN,GAAG;QACJ,WAAW,CAAC;QACZ,QAAQ;QAER,cAAA;UAAC,UAAU;UAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe;YACf,iBAAe;YACf,cAAY,aAAa,WAAW;YACpC,iBAAe,WAAW,KAAK;YAC/B;YACA,IAAI;YACH,GAAG;YACJ,KAAK;YACL,aAAa,qBAAqB,MAAM,aAAa,CAAC,UAAU;AAG9D,kBAAI,CAAC,YAAY,MAAM,WAAW,KAAK,MAAM,YAAY,OAAO;AAC9D,wBAAQ,cAAc,KAAK;cAC7B,OAAO;AAEL,sBAAM,eAAe;cACvB;YACF,CAAC;YACD,WAAW,qBAAqB,MAAM,WAAW,CAAC,UAAU;AAC1D,kBAAI,CAAC,KAAK,OAAO,EAAE,SAAS,MAAM,GAAG,EAAG,SAAQ,cAAc,KAAK;YACrE,CAAC;YACD,SAAS,qBAAqB,MAAM,SAAS,MAAM;AAGjD,oBAAM,wBAAwB,QAAQ,mBAAmB;AACzD,kBAAI,CAAC,cAAc,CAAC,YAAY,uBAAuB;AACrD,wBAAQ,cAAc,KAAK;cAC7B;YACF,CAAC;UAAA;QACH;MAAA;IACF;EAEJ;AACF;AAEA,YAAY,cAAc;AAM1B,IAAM,eAAe;AAarB,IAAM,cAAoB;EACxB,CAAC,OAAsC,iBAAiB;AACtD,UAAM,EAAE,aAAa,OAAO,YAAY,UAAU,GAAG,aAAa,IAAI;AACtE,UAAM,UAAU,eAAe,cAAc,WAAW;AACxD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,YAAY,cAAc,QAAQ,QAAQ,KAAK;AACrD,UAAM,aAAa,UAAU,QAAQ;AACrC,UAAM,+BAAqC,aAAO,UAAU;AAEtD,IAAA,gBAAU,MAAM;AACpB,YAAM,MAAM,sBAAsB,MAAO,6BAA6B,UAAU,KAAM;AACtF,aAAO,MAAM,qBAAqB,GAAG;IACvC,GAAG,CAAC,CAAC;AAEL,eACE,wBAAC,UAAA,EAAS,SAAS,cAAc,YAC9B,UAAA,CAAC,EAAE,QAAQ,UACV;MAAC,UAAU;MAAV;QACC,cAAY,aAAa,WAAW;QACpC,oBAAkB,QAAQ;QAC1B,MAAK;QACL,mBAAiB;QACjB,QAAQ,CAAC;QACT,IAAI;QACJ,UAAU;QACT,GAAG;QACJ,KAAK;QACL,OAAO;UACL,GAAG,MAAM;UACT,mBAAmB,6BAA6B,UAAU,OAAO;QACnE;QAEC,UAAA,WAAW;MAAA;IACd,EAAA,CAEJ;EAEJ;AACF;AAEA,YAAY,cAAc;AAI1B,SAAS,cAAc,QAAgB,OAAe;AACpD,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AAEA,SAAS,cAAc,QAAgB,OAAe;AACpD,SAAO,GAAG,MAAM,YAAY,KAAK;AACnC;AAEA,IAAMA,QAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU;", "names": ["Root"]}